import React, { useState } from 'react';
import './WebSearchInput.css';

const WebSearchInput = ({ onWebSearchToggle, disabled = false, isWebSearchMode = false }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleToggle = () => {
    if (disabled) return;
    onWebSearchToggle(!isWebSearchMode);
  };

  return (
    <div className="web-search-input">
      <button
        type="button"
        className={`web-search-input__button ${isWebSearchMode ? 'web-search-input__button--active' : ''}`}
        onClick={handleToggle}
        disabled={disabled}
        title={isWebSearchMode ? "Disable web search" : "Enable web search"}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
          <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
          <path d="M21 21l-4.35-4.35" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <circle cx="11" cy="11" r="3" stroke="currentColor" strokeWidth="1" fill={isWebSearchMode ? "currentColor" : "none"}/>
        </svg>
        {isWebSearchMode && (
          <span className="web-search-input__indicator">
            Web Search
          </span>
        )}
      </button>

      {/* Web Search Mode Indicator */}
      {isWebSearchMode && (
        <div className="web-search-input__mode-indicator">
          <div className="web-search-input__mode-content">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
              <path d="M21 21l-4.35-4.35" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span className="web-search-input__mode-text">
              Web search enabled - your query will search the internet
            </span>
            <button
              type="button"
              className="web-search-input__mode-close"
              onClick={handleToggle}
              title="Disable web search"
            >
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default WebSearchInput;
